import 'package:google_maps_flutter/google_maps_flutter.dart';

// Kampus joylashuvlari va yo'llar ma'lumotlari
class CampusLocation {
  final String id;
  final String name;
  final String nameUz;
  final String description;
  final String descriptionUz;
  final LatLng position;
  final String category;
  final List<String> keywords;

  CampusLocation({
    required this.id,
    required this.name,
    required this.nameUz,
    required this.description,
    required this.descriptionUz,
    required this.position,
    required this.category,
    required this.keywords,
  });
}

// Kampus yo'llari va nuqtalari
class CampusData {
  // Asosiy kampus joylashuvlari (namuna ma'lumotlar)
  static final List<CampusLocation> locations = [
    CampusLocation(
      id: 'main_building',
      name: 'Main Building',
      nameUz: 'Asosiy bino',
      description: 'Main administrative building',
      descriptionUz: 'Asosiy ma\'muriy bino',
      position: LatLng(41.2995, 69.2401), // Toshkent shahri koordinatalari (namuna)
      category: 'building',
      keywords: ['admin', 'office', 'main', 'asosiy', 'bino'],
    ),
    CampusLocation(
      id: 'library',
      name: 'University Library',
      nameUz: 'Universitet kutubxonasi',
      description: 'Central library with study areas',
      descriptionUz: 'O\'qish xonalari bilan markaziy kutubxona',
      position: LatLng(41.2985, 69.2411),
      category: 'library',
      keywords: ['library', 'books', 'study', 'kutubxona', 'kitob'],
    ),
    CampusLocation(
      id: 'cafeteria',
      name: 'Student Cafeteria',
      nameUz: 'Talabalar oshxonasi',
      description: 'Main dining hall for students',
      descriptionUz: 'Talabalar uchun asosiy ovqatlanish zali',
      position: LatLng(41.3005, 69.2395),
      category: 'dining',
      keywords: ['food', 'dining', 'cafeteria', 'ovqat', 'oshxona'],
    ),
    CampusLocation(
      id: 'computer_lab',
      name: 'Computer Laboratory',
      nameUz: 'Kompyuter laboratoriyasi',
      description: 'IT and computer science lab',
      descriptionUz: 'IT va kompyuter fanlari laboratoriyasi',
      position: LatLng(41.2975, 69.2421),
      category: 'laboratory',
      keywords: ['computer', 'lab', 'IT', 'kompyuter', 'laboratoriya'],
    ),
    CampusLocation(
      id: 'sports_center',
      name: 'Sports Center',
      nameUz: 'Sport markazi',
      description: 'Gymnasium and sports facilities',
      descriptionUz: 'Gimnastika zali va sport inshootlari',
      position: LatLng(41.3015, 69.2385),
      category: 'sports',
      keywords: ['sports', 'gym', 'fitness', 'sport', 'gimnastika'],
    ),
    CampusLocation(
      id: 'parking_main',
      name: 'Main Parking',
      nameUz: 'Asosiy avtoturargoh',
      description: 'Main parking area for students and staff',
      descriptionUz: 'Talabalar va xodimlar uchun asosiy avtoturargoh',
      position: LatLng(41.2965, 69.2431),
      category: 'parking',
      keywords: ['parking', 'car', 'vehicle', 'avtoturargoh', 'mashina'],
    ),
  ];

  // Kampus yo'llari (asosiy yo'nalishlar)
  static final List<List<LatLng>> campusRoutes = [
    // Asosiy yo'l - shimoldan janubga
    [
      LatLng(41.3025, 69.2401),
      LatLng(41.3015, 69.2401),
      LatLng(41.3005, 69.2401),
      LatLng(41.2995, 69.2401),
      LatLng(41.2985, 69.2401),
      LatLng(41.2975, 69.2401),
      LatLng(41.2965, 69.2401),
    ],
    // Sharqiy yo'l
    [
      LatLng(41.2995, 69.2381),
      LatLng(41.2995, 69.2391),
      LatLng(41.2995, 69.2401),
      LatLng(41.2995, 69.2411),
      LatLng(41.2995, 69.2421),
      LatLng(41.2995, 69.2431),
    ],
    // G'arbiy yo'l
    [
      LatLng(41.3005, 69.2381),
      LatLng(41.3005, 69.2391),
      LatLng(41.3005, 69.2401),
      LatLng(41.3005, 69.2411),
      LatLng(41.3005, 69.2421),
    ],
  ];

  // Joylashuvni qidirish
  static List<CampusLocation> searchLocations(String query) {
    if (query.isEmpty) return locations;
    
    final lowerQuery = query.toLowerCase();
    return locations.where((location) {
      return location.name.toLowerCase().contains(lowerQuery) ||
             location.nameUz.toLowerCase().contains(lowerQuery) ||
             location.description.toLowerCase().contains(lowerQuery) ||
             location.descriptionUz.toLowerCase().contains(lowerQuery) ||
             location.keywords.any((keyword) => 
               keyword.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  // Yaqin atrofdagi joylashuvlarni topish
  static List<CampusLocation> getNearbyLocations(LatLng userLocation, double radiusKm) {
    return locations.where((location) {
      final distance = _calculateDistance(userLocation, location.position);
      return distance <= radiusKm;
    }).toList();
  }

  // Ikki nuqta orasidagi masofani hisoblash (Haversine formula)
  static double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // Yer radiusi km da
    
    final double lat1Rad = point1.latitude * (3.14159 / 180);
    final double lat2Rad = point2.latitude * (3.14159 / 180);
    final double deltaLatRad = (point2.latitude - point1.latitude) * (3.14159 / 180);
    final double deltaLngRad = (point2.longitude - point1.longitude) * (3.14159 / 180);

    final double a = (deltaLatRad / 2).sin() * (deltaLatRad / 2).sin() +
        lat1Rad.cos() * lat2Rad.cos() *
        (deltaLngRad / 2).sin() * (deltaLngRad / 2).sin();
    final double c = 2 * (a.sqrt()).asin();

    return earthRadius * c;
  }

  // Kategoriya bo'yicha joylashuvlarni olish
  static List<CampusLocation> getLocationsByCategory(String category) {
    return locations.where((location) => location.category == category).toList();
  }

  // Barcha kategoriyalarni olish
  static List<String> getAllCategories() {
    return locations.map((location) => location.category).toSet().toList();
  }
}

// Navigatsiya yo'nalishlari
class NavigationInstruction {
  final String instruction;
  final String instructionUz;
  final double distance;
  final String direction;
  final LatLng position;

  NavigationInstruction({
    required this.instruction,
    required this.instructionUz,
    required this.distance,
    required this.direction,
    required this.position,
  });
}

// Turn-by-turn navigatsiya uchun yo'nalishlar generatori
class NavigationHelper {
  static List<NavigationInstruction> generateInstructions(List<LatLng> route) {
    List<NavigationInstruction> instructions = [];
    
    if (route.length < 2) return instructions;

    // Boshlang'ich yo'nalish
    instructions.add(NavigationInstruction(
      instruction: "Start your journey",
      instructionUz: "Sayohatingizni boshlang",
      distance: 0.0,
      direction: "start",
      position: route.first,
    ));

    // Yo'l bo'ylab yo'nalishlar
    for (int i = 1; i < route.length - 1; i++) {
      final distance = CampusData._calculateDistance(route[i-1], route[i]) * 1000; // metrda
      final direction = _getDirection(route[i-1], route[i], route[i+1]);
      
      instructions.add(NavigationInstruction(
        instruction: _getInstructionText(direction, distance),
        instructionUz: _getInstructionTextUz(direction, distance),
        distance: distance,
        direction: direction,
        position: route[i],
      ));
    }

    // Yakuniy yo'nalish
    final finalDistance = CampusData._calculateDistance(route[route.length-2], route.last) * 1000;
    instructions.add(NavigationInstruction(
      instruction: "You have arrived at your destination",
      instructionUz: "Siz manzilga yetib keldingiz",
      distance: finalDistance,
      direction: "arrive",
      position: route.last,
    ));

    return instructions;
  }

  static String _getDirection(LatLng from, LatLng current, LatLng to) {
    // Soddalashtirilgan yo'nalish aniqlash
    final bearing1 = _calculateBearing(from, current);
    final bearing2 = _calculateBearing(current, to);
    final angleDiff = (bearing2 - bearing1 + 360) % 360;

    if (angleDiff < 45 || angleDiff > 315) return "straight";
    if (angleDiff >= 45 && angleDiff < 135) return "right";
    if (angleDiff >= 135 && angleDiff < 225) return "u_turn";
    return "left";
  }

  static double _calculateBearing(LatLng from, LatLng to) {
    final lat1 = from.latitude * (3.14159 / 180);
    final lat2 = to.latitude * (3.14159 / 180);
    final deltaLng = (to.longitude - from.longitude) * (3.14159 / 180);

    final y = deltaLng.sin() * lat2.cos();
    final x = lat1.cos() * lat2.sin() - lat1.sin() * lat2.cos() * deltaLng.cos();

    return ((y.atan2(x)) * (180 / 3.14159) + 360) % 360;
  }

  static String _getInstructionText(String direction, double distance) {
    final distanceText = distance < 1000 
        ? "${distance.round()}m" 
        : "${(distance/1000).toStringAsFixed(1)}km";
    
    switch (direction) {
      case "straight": return "Continue straight for $distanceText";
      case "right": return "Turn right in $distanceText";
      case "left": return "Turn left in $distanceText";
      case "u_turn": return "Make a U-turn in $distanceText";
      default: return "Continue for $distanceText";
    }
  }

  static String _getInstructionTextUz(String direction, double distance) {
    final distanceText = distance < 1000 
        ? "${distance.round()}m" 
        : "${(distance/1000).toStringAsFixed(1)}km";
    
    switch (direction) {
      case "straight": return "$distanceText to'g'ri yo'nalishda davom eting";
      case "right": return "$distanceText dan keyin o'ngga burilin";
      case "left": return "$distanceText dan keyin chapga burilin";
      case "u_turn": return "$distanceText dan keyin orqaga burilin";
      default: return "$distanceText davom eting";
    }
  }
}
