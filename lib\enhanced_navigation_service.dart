import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_project/campus_locations.dart';
import 'package:google_maps_project/routes.dart';

// Yaxshilangan navigatsiya xizmati
class EnhancedNavigationService {
  final RouteService _routeService = RouteService();
  StreamSubscription<Position>? _locationSubscription;
  
  // Hozirgi navigatsiya holati
  NavigationState? _currentNavigation;
  
  // Callback funksiyalar
  Function(NavigationState)? onNavigationUpdate;
  Function(NavigationInstruction)? onInstructionUpdate;
  Function(String)? onNavigationComplete;
  Function(String)? onNavigationError;

  // Navigatsiya holatini boshlash
  Future<void> startNavigation({
    required LatLng destination,
    required String transportMode,
    LatLng? startLocation,
  }) async {
    try {
      // Boshlang'ich joylashuvni aniqlash
      LatLng start = startLocation ?? await _getCurrentLocation();
      
      // Yo'lni hisoblash
      final routeData = await _routeService.getRoute(start, destination, transportMode);
      
      if (routeData['coordinates'] == null || routeData['coordinates'].isEmpty) {
        throw Exception('Yo\'l topilmadi');
      }

      // Navigatsiya yo'nalishlarini yaratish
      final instructions = NavigationHelper.generateInstructions(routeData['coordinates']);
      
      // Navigatsiya holatini yaratish
      _currentNavigation = NavigationState(
        route: List<LatLng>.from(routeData['coordinates']),
        instructions: instructions,
        currentInstructionIndex: 0,
        totalDistance: routeData['distance'].toDouble(),
        totalDuration: routeData['duration'].toDouble(),
        transportMode: transportMode,
        destination: destination,
        isActive: true,
      );

      // Joylashuv kuzatuvini boshlash
      _startLocationTracking();
      
      // Callback chaqirish
      if (onNavigationUpdate != null) {
        onNavigationUpdate!(_currentNavigation!);
      }

    } catch (e) {
      if (onNavigationError != null) {
        onNavigationError!('Navigatsiya xatosi: ${e.toString()}');
      }
    }
  }

  // Joylashuv kuzatuvini boshlash
  void _startLocationTracking() {
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 5, // 5 metr har safar yangilanish
    );

    _locationSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen((Position position) {
      _updateNavigationWithLocation(position);
    });
  }

  // Joylashuv yangilanishi bilan navigatsiyani yangilash
  void _updateNavigationWithLocation(Position position) {
    if (_currentNavigation == null || !_currentNavigation!.isActive) return;

    final currentLocation = LatLng(position.latitude, position.longitude);
    
    // Manzilga yetib kelganligini tekshirish
    final distanceToDestination = _calculateDistance(
      currentLocation, 
      _currentNavigation!.destination
    );

    if (distanceToDestination < 20) { // 20 metr yaqinlashganda
      _completeNavigation();
      return;
    }

    // Hozirgi yo'nalishni yangilash
    _updateCurrentInstruction(currentLocation);
    
    // Navigatsiya holatini yangilash
    _currentNavigation = _currentNavigation!.copyWith(
      currentLocation: currentLocation,
      remainingDistance: _calculateRemainingDistance(currentLocation),
    );

    // Callback chaqirish
    if (onNavigationUpdate != null) {
      onNavigationUpdate!(_currentNavigation!);
    }
  }

  // Hozirgi yo'nalishni yangilash
  void _updateCurrentInstruction(LatLng currentLocation) {
    if (_currentNavigation == null) return;

    final instructions = _currentNavigation!.instructions;
    final currentIndex = _currentNavigation!.currentInstructionIndex;

    if (currentIndex < instructions.length) {
      final currentInstruction = instructions[currentIndex];
      final distanceToInstruction = _calculateDistance(
        currentLocation, 
        currentInstruction.position
      );

      // Agar yo'nalishga yaqinlashgan bo'lsa, keyingisiga o'tish
      if (distanceToInstruction < 30 && currentIndex < instructions.length - 1) {
        _currentNavigation = _currentNavigation!.copyWith(
          currentInstructionIndex: currentIndex + 1,
        );

        // Yangi yo'nalish haqida xabar berish
        if (onInstructionUpdate != null && currentIndex + 1 < instructions.length) {
          onInstructionUpdate!(instructions[currentIndex + 1]);
        }
      }
    }
  }

  // Qolgan masofani hisoblash
  double _calculateRemainingDistance(LatLng currentLocation) {
    if (_currentNavigation == null) return 0.0;

    final route = _currentNavigation!.route;
    final currentIndex = _currentNavigation!.currentInstructionIndex;
    
    double remainingDistance = 0.0;
    
    // Hozirgi joylashuvdan keyingi nuqtagacha
    if (currentIndex < route.length) {
      remainingDistance += _calculateDistance(currentLocation, route[currentIndex]);
      
      // Qolgan yo'l nuqtalari orasidagi masofa
      for (int i = currentIndex; i < route.length - 1; i++) {
        remainingDistance += _calculateDistance(route[i], route[i + 1]);
      }
    }
    
    return remainingDistance * 1000; // metrda qaytarish
  }

  // Navigatsiyani yakunlash
  void _completeNavigation() {
    if (_currentNavigation != null) {
      _currentNavigation = _currentNavigation!.copyWith(isActive: false);
      
      if (onNavigationComplete != null) {
        onNavigationComplete!('Manzilga muvaffaqiyatli yetib keldingiz!');
      }
    }
    
    stopNavigation();
  }

  // Navigatsiyani to'xtatish
  void stopNavigation() {
    _locationSubscription?.cancel();
    _currentNavigation = null;
  }

  // Hozirgi joylashuvni olish
  Future<LatLng> _getCurrentLocation() async {
    final position = await Geolocator.getCurrentPosition(
      desiredAccuracy: LocationAccuracy.high,
    );
    return LatLng(position.latitude, position.longitude);
  }

  // Ikki nuqta orasidagi masofani hisoblash (km da)
  double _calculateDistance(LatLng point1, LatLng point2) {
    return Geolocator.distanceBetween(
      point1.latitude,
      point1.longitude,
      point2.latitude,
      point2.longitude,
    ) / 1000;
  }

  // Yaqin atrofdagi kampus joylashuvlarini olish
  Future<List<CampusLocation>> getNearbyLocations(LatLng userLocation) async {
    return CampusData.getNearbyLocations(userLocation, 1.0); // 1 km radius
  }

  // Kampus yo'llarini olish
  List<Polyline> getCampusRoutes() {
    List<Polyline> polylines = [];
    
    for (int i = 0; i < CampusData.campusRoutes.length; i++) {
      polylines.add(Polyline(
        polylineId: PolylineId('campus_route_$i'),
        points: CampusData.campusRoutes[i],
        color: Colors.grey.withOpacity(0.6),
        width: 3,
        patterns: [PatternItem.dash(10.0), PatternItem.gap(5.0)],
      ));
    }
    
    return polylines;
  }

  // Kampus markerlarini olish
  Set<Marker> getCampusMarkers() {
    Set<Marker> markers = {};
    
    for (final location in CampusData.locations) {
      markers.add(Marker(
        markerId: MarkerId(location.id),
        position: location.position,
        infoWindow: InfoWindow(
          title: location.nameUz,
          snippet: location.descriptionUz,
        ),
        icon: _getMarkerIcon(location.category),
      ));
    }
    
    return markers;
  }

  // Kategoriya bo'yicha marker ikonini olish
  BitmapDescriptor _getMarkerIcon(String category) {
    switch (category) {
      case 'building':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue);
      case 'library':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen);
      case 'dining':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueOrange);
      case 'laboratory':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueCyan);
      case 'sports':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueYellow);
      case 'parking':
        return BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueViolet);
      default:
        return BitmapDescriptor.defaultMarker;
    }
  }

  // Hozirgi navigatsiya holatini olish
  NavigationState? get currentNavigation => _currentNavigation;
}

// Navigatsiya holati
class NavigationState {
  final List<LatLng> route;
  final List<NavigationInstruction> instructions;
  final int currentInstructionIndex;
  final double totalDistance;
  final double totalDuration;
  final double? remainingDistance;
  final String transportMode;
  final LatLng destination;
  final LatLng? currentLocation;
  final bool isActive;

  NavigationState({
    required this.route,
    required this.instructions,
    required this.currentInstructionIndex,
    required this.totalDistance,
    required this.totalDuration,
    this.remainingDistance,
    required this.transportMode,
    required this.destination,
    this.currentLocation,
    required this.isActive,
  });

  NavigationState copyWith({
    List<LatLng>? route,
    List<NavigationInstruction>? instructions,
    int? currentInstructionIndex,
    double? totalDistance,
    double? totalDuration,
    double? remainingDistance,
    String? transportMode,
    LatLng? destination,
    LatLng? currentLocation,
    bool? isActive,
  }) {
    return NavigationState(
      route: route ?? this.route,
      instructions: instructions ?? this.instructions,
      currentInstructionIndex: currentInstructionIndex ?? this.currentInstructionIndex,
      totalDistance: totalDistance ?? this.totalDistance,
      totalDuration: totalDuration ?? this.totalDuration,
      remainingDistance: remainingDistance ?? this.remainingDistance,
      transportMode: transportMode ?? this.transportMode,
      destination: destination ?? this.destination,
      currentLocation: currentLocation ?? this.currentLocation,
      isActive: isActive ?? this.isActive,
    );
  }

  // Hozirgi yo'nalishni olish
  NavigationInstruction? get currentInstruction {
    if (currentInstructionIndex < instructions.length) {
      return instructions[currentInstructionIndex];
    }
    return null;
  }

  // Keyingi yo'nalishni olish
  NavigationInstruction? get nextInstruction {
    if (currentInstructionIndex + 1 < instructions.length) {
      return instructions[currentInstructionIndex + 1];
    }
    return null;
  }
}
